<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>core-datasync</artifactId>
    <version>1.0.0</version>
    <name>core-datasync</name>
    <description>sync data from the third party system to our system, for example, sync jira issue to redshift</description>

    <parent>
        <groupId>com.sayweee</groupId>
        <artifactId>core-parent</artifactId>
        <version>1.1.10</version>
        <relativePath/>
    </parent>

    <properties>
        <revision>1.0.0</revision>
        <maven.compiler.release>21</maven.compiler.release>
        <core-security-spring-boot-starter.version>1.0.6</core-security-spring-boot-starter.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.sayweee</groupId>
            <artifactId>core-framework</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sayweee</groupId>
            <artifactId>core-security-spring-boot-starter</artifactId>
            <version>${core-security-spring-boot-starter.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.atlassian.jira</groupId>
            <artifactId>jira-rest-java-client-core</artifactId>
            <version>6.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.atlassian.jira</groupId>
            <artifactId>jira-rest-java-client-api</artifactId>
            <version>6.0.2</version>
        </dependency>
        <dependency>
            <groupId>io.atlassian.fugue</groupId>
            <artifactId>fugue</artifactId>
            <version>6.1.2</version>
            <scope>provided</scope>
        </dependency>

        <!-- Redshift JDBC Driver -->
        <dependency>
            <groupId>com.amazon.redshift</groupId>
            <artifactId>redshift-jdbc42</artifactId>
            <version>2.1.0.30</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.2.18</version>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>5.3.31</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <testSourceDirectory>${project.basedir}/src/test/groovy</testSourceDirectory>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <inherited>true</inherited>
                <version>3.5.2</version>
                <configuration>
                    <forkCount>1</forkCount>
                    <reuseForks>false</reuseForks>
                    <argLine>-XX:+EnableDynamicAgentLoading</argLine>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.4.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <inherited>true</inherited>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.34</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>nexus sayweee</id>
            <name>nexus sayweee</name>
            <url>https://maven.sayweee.net/repository/maven-public</url>
        </repository>
        <repository>
            <id>atlassian-public</id>
            <url>https://packages.atlassian.com/maven/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <!-- 备用仓库 -->
        <repository>
            <id>atlassian-maven2</id>
            <url>https://maven.atlassian.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
