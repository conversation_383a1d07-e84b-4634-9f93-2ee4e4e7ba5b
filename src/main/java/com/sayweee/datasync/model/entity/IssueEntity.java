package com.sayweee.datasync.model.entity;

import software.amazon.ion.Decimal;

import java.time.LocalDate;
import java.time.OffsetDateTime;

public record IssueEntity(
        Long id,
        String summary,
        String status,
        String priority,
        String issuetype,
        OffsetDateTime created,
        String projectId,
        String projectName,
        String projectKey,
        String issueKey,
        String issueSource,
        String issueModule,
        String issueSubModule,
        OffsetDateTime fixVersionDate,
        String fixVersion,
        LocalDate dueDate,
        Decimal storyPoints,
        String resolution,
        OffsetDateTime resolved,
        String issueUserviewCategory,
        String issueAffectsPlatform,
        OffsetDateTime updated,
        OffsetDateTime inDate,
        String purpose,
        OffsetDateTime rfqPlanDate,
        OffsetDateTime rfqDate,
        LocalDate planReleaseDate,
        String reporter,
        String developer,
        String tester,
        Integer originalEstimate,
        String smokeCheck,
        String legacyIssue,
        String qaTeam,
        String workCategory,
        String parentKey,
        String rfqDelayComment,
        String releaseDelayComment,
        String assignee,
        String additionalRequirements,
        Integer parentId,
        Integer timeSpent,
        LocalDate etaShanghai,
        String remark,
        String components,
        String bugDescriptionCheck
) {
}