package com.sayweee.datasync.service;


import com.sayweee.datasync.fetcher.jira.IssueFetcher;
import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
import com.sayweee.datasync.model.entity.IssueEntity;
import com.sayweee.datasync.model.request.JiraSyncRequest;
import com.sayweee.datasync.writer.jira.IssueWriter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class JiraService {
    private final IssueFetcher issueFetcher;
    private final IssueWriter issueWriter;
    private final static int BATCH_SIZE = 100;
    AtomicInteger total = new AtomicInteger(0);

    public void syncIssues(String taskId, JiraSyncRequest request) {
        log.info("开始同步Jira任务: {}", taskId);
        List<IssueEntity> batch = new ArrayList<>(BATCH_SIZE);
        Stream<JiraIngestionPayload> issuesStream = issueFetcher.getIssues(taskId, request);

        issuesStream.forEach(payload -> {
            batch.add(payload.issue());
            total.incrementAndGet();
            if (batch.size() >= BATCH_SIZE) {
                log.info("task [{}] got {} jira issues", taskId, batch.size());
                issueWriter.write(taskId, batch);
                log.info("task [{}] write {} jira issues", taskId, batch.size());
                batch.clear();
            }
        });

        if (!CollectionUtils.isEmpty(batch)) {
            log.info("task [{}] got {} jira issues", taskId, batch.size());
            issueWriter.write(taskId, batch);
            log.info("task [{}] write {} jira issues", taskId, batch.size());
        }

        log.info("task [{}] synced successfully，total {}", taskId, total.get());
    }
}