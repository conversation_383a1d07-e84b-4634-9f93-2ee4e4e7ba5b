package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.Issue;
import com.atlassian.jira.rest.client.api.domain.IssueField;
import com.atlassian.jira.rest.client.api.domain.Version;
import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
import com.sayweee.datasync.model.entity.IssueEntity;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jettison.json.JSONObject;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class JiraPayloadParser {
    public List<JiraIngestionPayload> parse(Iterable<Issue> issues) {
        log.info("Parsing Jira issues...");

        return java.util.stream.StreamSupport.stream(issues.spliterator(), false)
                .map(this::parseIssue)
                .collect(Collectors.toList());
    }

    private JiraIngestionPayload parseIssue(Issue issue) {
        IssueEntity issueEntity = new IssueEntity(
                issue.getId(), // id
                issue.getSummary(), // summary
                issue.getStatus() != null ? issue.getStatus().getName() : null, // status
                issue.getPriority() != null ? issue.getPriority().getName() : null, // priority
                issue.getIssueType() != null ? issue.getIssueType().getName() : null, // issuetype
                convertDateTime(issue.getCreationDate()), // created
                issue.getProject() != null ? issue.getProject().getId().toString() : null, // projectId
                issue.getProject() != null ? issue.getProject().getName() : null, // projectName
                issue.getProject() != null ? issue.getProject().getKey() : null, // projectKey
                issue.getKey(), // issueKey

                // issueSource (customfield_10965) - 需要通过其他方式获取
                getCustomFieldValue(issue, "customfield_10965"),

                // issueModule (customfield_11043) - 需要通过其他方式获取
                getCustomFieldValue(issue, "customfield_11043"),

                // issueSubModule (customfield_11043.child) - 从 parent field 中提取 child.value
                getCustomFieldValue(issue, "customfield_11043", "child"),
                getMaxReleaseVersionDate(issue), // fixVersionDate
                getMaxReleaseVersionName(issue), // fixVersion

                convertLocalDate(issue.getDueDate()), // dueDate (duedate)

                // storyPoints (customfield_10500) - 需要通过其他方式获取
                getCustomFieldValueAsDecimal(issue, "customfield_10500"),

                issue.getResolution() != null ? issue.getResolution().getName() : null, // resolution
                convertDateTime(getCustomFieldValueAsDateTime(issue, "resolutiondate")), // resolved (resolutiondate) - Issue类中没有此方法
                // issueUserviewCategory (customfield_11035) - 需要通过其他方式获取
                getCustomFieldValue(issue, "customfield_11035"),

                // issueAffectsPlatform (customfield_11034) - 需要通过其他方式获取
                getCustomFieldValue(issue, "customfield_11034"),

                convertDateTime(issue.getUpdateDate()), // updated

                OffsetDateTime.now(), // inDate

                // purpose (customfield_10951) - 需要通过其他方式获取
                getCustomFieldValue(issue, "customfield_10951"),

                // rfqPlanDate (customfield_11014) - 需要通过其他方式获取
                convertDateTime(getCustomFieldValueAsDateTime(issue, "customfield_11014")),

                // rfqDate (customfield_11015) - 需要通过其他方式获取
                convertDateTime(getCustomFieldValueAsDateTime(issue, "customfield_11015")),

                // planReleaseDate (customfield_11044) - 需要通过其他方式获取
                convertLocalDate(getCustomFieldValueAsDateTime(issue, "customfield_11044")),

                issue.getReporter() != null ? issue.getReporter().getDisplayName() : null, // reporter

                // developer (customfield_10601) - 需要通过其他方式获取
                getCustomFieldValue(issue, "customfield_10601"),

                // tester (customfield_10964) - 需要通过其他方式获取
                getCustomFieldValue(issue, "customfield_10964"),

                // originalEstimate (timeoriginalestimate) - 需要通过其他方式获取
                getCustomFieldValueAsInteger(issue, "timeoriginalestimate"),

                // smokeCheck (customfield_11046) - 需要通过其他方式获取
                getCustomFieldValue(issue, "customfield_11046"),

                // legacyIssue (customfield_11048) - 需要通过其他方式获取
                getCustomFieldValue(issue, "customfield_11048"),

                // qaTeam (customfield_11049) - 需要通过其他方式获取
                getCustomFieldValue(issue, "customfield_11049"),

                // workCategory (customfield_11047) - 需要通过其他方式获取
                getCustomFieldValue(issue, "customfield_11047"),

                // parentKey
                getCustomFieldValue(issue, "parent", "key"),

                // rfqDelayComment (customfield_11079) - 需要通过其他方式获取
                getCustomFieldValue(issue, "customfield_11079"),

                // releaseDelayComment (customfield_11080) - 需要通过其他方式获取
                getCustomFieldValue(issue, "customfield_11080"),

                issue.getAssignee() != null ? issue.getAssignee().getDisplayName() : null, // assignee

                // additionalRequirements (customfield_11070) - 需要通过其他方式获取
                getCustomFieldValue(issue, "customfield_11070"),

                getCustomFieldValueAsInteger(issue, "parent", "id"),

                // timeSpent (aggregatetimespent) - 需要通过其他方式获取
                getCustomFieldValueAsInteger(issue, "aggregatetimespent"),

                // etaShanghai (customfield_11078) - 需要通过其他方式获取
                convertLocalDate(getCustomFieldValueAsDateTime(issue, "customfield_11078")),

                null, // remark

                // components - 需要从components字段中提取
                null,

                null  // bugDescriptionCheck
        );

        if (issue.getId() == 294428) {
            log.info("issue: {}", issue);
        }

        return new JiraIngestionPayload(issueEntity, null, null);
    }

    /**
     * 获取自定义字段的值 - 需要在实际实现中完成
     * 当前JIRA客户端API不支持直接访问自定义字段
     *
     * @param issue   JIRA Issue对象
     * @param fieldId 自定义字段ID
     * @return 字段值字符串
     */
    private String getCustomFieldValue(Issue issue, String fieldId) {
        try {
            return Optional.ofNullable(issue.getField(fieldId))
                    .map(IssueField::getValue)
                    .flatMap(fieldValue -> {
                        return switch (fieldValue) {
                            case String s -> Optional.of(s);
                            case JSONObject jo -> Optional.ofNullable(jo.optString("value", null));
                            default -> Optional.empty();
                        };
                    })
                    .orElse(null); // 如果整个链条中任何一步为空，最终返回 null
        } catch (Exception e) {
            log.warn("Failed to parse child value from custom field: {}", fieldId, e);
            return null;
        }
    }

    private String getCustomFieldValue(Issue issue, String fieldId, String childFieldId) {
        try {
            return Optional.ofNullable(issue.getField(fieldId))
                    .map(IssueField::getValue)
                    .flatMap(fieldValue -> {
                        return switch (fieldValue) {
                            case String s -> Optional.of(s);
                            case JSONObject jo -> Optional.ofNullable(jo.optJSONObject(childFieldId))
                                    .flatMap(childObj -> Optional.ofNullable(childObj.optString("value", null)));
                            default -> Optional.empty();
                        };
                    })
                    .orElse(null);
        } catch (Exception e) {
            log.warn("Failed to parse child value from custom field: {}", fieldId, e);
            return null;
        }
    }

    /**
     * 获取自定义字段的值作为Decimal - 需要在实际实现中完成
     *
     * @param issue   JIRA Issue对象
     * @param fieldId 自定义字段ID
     * @return 字段值作为Decimal
     */
    private software.amazon.ion.Decimal getCustomFieldValueAsDecimal(Issue issue, String fieldId) {
        // TODO: 实现自定义字段值的获取逻辑
        return null;
    }

    /**
     * 获取自定义字段的值作为DateTime - 需要在实际实现中完成
     *
     * @param issue   JIRA Issue对象
     * @param fieldId 自定义字段ID
     * @return 字段值作为DateTime
     */
    private DateTime getCustomFieldValueAsDateTime(Issue issue, String fieldId) {
        // TODO: 实现自定义字段值的获取逻辑
        return null;
    }

    /**
     * 获取自定义字段的值作为Integer - 需要在实际实现中完成
     *
     * @param issue   JIRA Issue对象
     * @param fieldId 自定义字段ID
     * @return 字段值作为Integer
     */
    private Integer getCustomFieldValueAsInteger(Issue issue, String fieldId) {
        // TODO: 实现自定义字段值的获取逻辑
        return null;
    }

    private OffsetDateTime convertDateTime(DateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return OffsetDateTime.ofInstant(Instant.ofEpochMilli(dateTime.getMillis()), ZoneOffset.UTC);
    }

    private LocalDate convertLocalDate(DateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return convertDateTime(dateTime).toLocalDate();
    }

    /**
     * 从Issue的fixVersions中找到releaseDate最大的版本
     *
     * @param issue JIRA Issue对象
     * @return 具有最大releaseDate的Version对象，如果没有fixVersions或releaseDate则返回null
     */
    private Version getMaxReleaseVersion(Issue issue) {
        try {
            if (issue.getFixVersions() == null) {
                return null;
            }

            // 将Iterable转换为Stream并处理
            return java.util.stream.StreamSupport.stream(issue.getFixVersions().spliterator(), false)
                    .filter(version -> version.getReleaseDate() != null)  // 只考虑有releaseDate的版本
                    .max((v1, v2) -> v1.getReleaseDate().compareTo(v2.getReleaseDate()))  // 按releaseDate排序，取最大的
                    .orElse(null);
        } catch (Exception e) {
            log.warn("Failed to get max release version from fixVersions", e);
            return null;
        }
    }

    /**
     * 获取fixVersions中releaseDate最大的版本的releaseDate
     *
     * @param issue JIRA Issue对象
     * @return 最大releaseDate转换为OffsetDateTime，如果没有则返回null
     */
    private OffsetDateTime getMaxReleaseVersionDate(Issue issue) {
        com.atlassian.jira.rest.client.api.domain.Version maxVersion = getMaxReleaseVersion(issue);
        return maxVersion != null ? convertDateTime(maxVersion.getReleaseDate()) : null;
    }

    /**
     * 获取fixVersions中releaseDate最大的版本的name
     *
     * @param issue JIRA Issue对象
     * @return 最大releaseDate版本的名称，如果没有则返回null
     */
    private String getMaxReleaseVersionName(Issue issue) {
        com.atlassian.jira.rest.client.api.domain.Version maxVersion = getMaxReleaseVersion(issue);
        return maxVersion != null ? maxVersion.getName() : null;
    }
}