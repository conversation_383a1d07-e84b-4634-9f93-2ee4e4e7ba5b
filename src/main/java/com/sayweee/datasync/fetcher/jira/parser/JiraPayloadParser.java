package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.*;
import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
import com.sayweee.datasync.model.entity.IssueEntity; // 假设 IssueEntity 在此包下
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;
import software.amazon.ion.Decimal;

import java.time.Instant;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Slf4j
@Component
public class JiraPayloadParser {

    private static final String FIELD_ISSUE_SOURCE = "customfield_10965";
    private static final String FIELD_ISSUE_MODULE = "customfield_11043";
    private static final String FIELD_STORY_POINTS = "customfield_10500";
    private static final String FIELD_RESOLUTION_DATE = "resolutiondate";
    private static final String FIELD_USERVIEW_CATEGORY = "customfield_11035";
    private static final String FIELD_AFFECTS_PLATFORM = "customfield_11034";
    private static final String FIELD_PURPOSE = "customfield_10951";
    private static final String FIELD_RFQ_PLAN_DATE = "customfield_11014";
    private static final String FIELD_RFQ_DATE = "customfield_11015";
    private static final String FIELD_PLAN_RELEASE_DATE = "customfield_11044";
    private static final String FIELD_DEVELOPER = "customfield_10601";
    private static final String FIELD_TESTER = "customfield_10964";
    private static final String FIELD_ORIGINAL_ESTIMATE = "timeoriginalestimate";
    private static final String FIELD_SMOKE_CHECK = "customfield_11046";
    private static final String FIELD_LEGACY_ISSUE = "customfield_11048";
    private static final String FIELD_QA_TEAM = "customfield_11049";
    private static final String FIELD_WORK_CATEGORY = "customfield_11047";
    private static final String FIELD_PARENT = "parent";
    private static final String FIELD_RFQ_DELAY_COMMENT = "customfield_11079";
    private static final String FIELD_RELEASE_DELAY_COMMENT = "customfield_11080";
    private static final String FIELD_ADDITIONAL_REQUIREMENTS = "customfield_11070";
    private static final String FIELD_TIME_SPENT = "aggregatetimespent";
    private static final String FIELD_ETA_SHANGHAI = "customfield_11078";
    private static final String FIELD_BUG_DESCRIPTION_CHECK = "customfield_11070";

    public List<JiraIngestionPayload> parse(Iterable<Issue> issues) {
        log.info("Parsing Jira issues...");
        return StreamSupport.stream(issues.spliterator(), false)
                .map(this::parseIssue)
                .collect(Collectors.toList());
    }

    private JiraIngestionPayload parseIssue(Issue issue) {
        Long id = issue.getId();
        String summary = issue.getSummary();
        String status = Optional.ofNullable(issue.getStatus()).map(s -> s.getName()).orElse(null);
        String priority = Optional.ofNullable(issue.getPriority()).map(p -> p.getName()).orElse(null);
        String issueType = Optional.ofNullable(issue.getIssueType()).map(it -> it.getName()).orElse(null);
        OffsetDateTime created = convertDateTime(issue.getCreationDate());
        String projectId = Optional.ofNullable(issue.getProject()).map(p -> p.getId().toString()).orElse(null);
        String projectName = Optional.ofNullable(issue.getProject()).map(p -> p.getName()).orElse(null);
        String projectKey = Optional.ofNullable(issue.getProject()).map(p -> p.getKey()).orElse(null);
        String issueKey = issue.getKey();
        LocalDate dueDate = convertLocalDate(issue.getDueDate());
        String resolution = Optional.ofNullable(issue.getResolution()).map(r -> r.getName()).orElse(null);
        OffsetDateTime updated = convertDateTime(issue.getUpdateDate());
        OffsetDateTime inDate = OffsetDateTime.now(ZoneOffset.UTC); // 建议使用UTC以保持一致
        String reporter = Optional.ofNullable(issue.getReporter()).map(u -> u.getDisplayName()).orElse(null);
        String assignee = Optional.ofNullable(issue.getAssignee()).map(u -> u.getDisplayName()).orElse(null);
        String components = getComponentsAsString(issue);
        String remark = null; // remark 字段当前无数据源，设为 null

        // FixVersion 相关字段
        Optional<Version> maxVersionOpt = getMaxReleaseVersion(issue);
        OffsetDateTime fixVersionDate = maxVersionOpt.map(v -> convertDateTime(v.getReleaseDate())).orElse(null);
        String fixVersion = maxVersionOpt.map(Version::getName).orElse(null);

        // 自定义字段
        String issueSource = getCustomFieldValue(issue, FIELD_ISSUE_SOURCE, this::extractStringValue);
        String issueModule = getCustomFieldValue(issue, FIELD_ISSUE_MODULE, this::extractStringValue);
        String issueSubModule = getCustomFieldValue(issue, FIELD_ISSUE_MODULE, obj -> extractChildValue(obj, "child"));
        Decimal storyPoints = getCustomFieldValue(issue, FIELD_STORY_POINTS, this::convertToDecimal);
        OffsetDateTime resolved = getCustomFieldValue(issue, FIELD_RESOLUTION_DATE, this::convertToOffsetDateTime);
        String userViewCategory = getCustomFieldValue(issue, FIELD_USERVIEW_CATEGORY, this::extractStringValue);
        String affectsPlatform = getCustomFieldValue(issue, FIELD_AFFECTS_PLATFORM, this::extractStringValue);
        String purpose = getCustomFieldValue(issue, FIELD_PURPOSE, this::extractStringValue);
        OffsetDateTime rfqPlanDate = getCustomFieldValue(issue, FIELD_RFQ_PLAN_DATE, this::convertToOffsetDateTime);
        OffsetDateTime rfqDate = getCustomFieldValue(issue, FIELD_RFQ_DATE, this::convertToOffsetDateTime);
        LocalDate planReleaseDate = getCustomFieldValue(issue, FIELD_PLAN_RELEASE_DATE, this::convertToLocalDate);
        String developer = getCustomFieldValue(issue, FIELD_DEVELOPER, this::extractStringValue);
        String tester = getCustomFieldValue(issue, FIELD_TESTER, this::extractStringValue);
        Integer originalEstimate = getCustomFieldValue(issue, FIELD_ORIGINAL_ESTIMATE, this::convertToInteger);
        String smokeCheck = getCustomFieldValue(issue, FIELD_SMOKE_CHECK, this::extractStringValue);
        String legacyIssue = getCustomFieldValue(issue, FIELD_LEGACY_ISSUE, this::extractStringValue);
        String qaTeam = getCustomFieldValue(issue, FIELD_QA_TEAM, this::extractStringValue);
        String workCategory = getCustomFieldValue(issue, FIELD_WORK_CATEGORY, this::extractStringValue);
        String parentKey = getCustomFieldValue(issue, FIELD_PARENT, obj -> extractChildValue(obj, "key"));
        String rfqDelayComment = getCustomFieldValue(issue, FIELD_RFQ_DELAY_COMMENT, this::extractStringValue);
        String releaseDelayComment = getCustomFieldValue(issue, FIELD_RELEASE_DELAY_COMMENT, this::extractStringValue);
        String additionalRequirements = getCustomFieldValue(issue, FIELD_ADDITIONAL_REQUIREMENTS, this::extractStringValue);
        Integer parentId = getCustomFieldValue(issue, FIELD_PARENT, obj -> convertToInteger(extractChildValue(obj, "id")));
        Integer timeSpent = getCustomFieldValue(issue, FIELD_TIME_SPENT, this::convertToInteger);
        LocalDate etaShanghai = getCustomFieldValue(issue, FIELD_ETA_SHANGHAI, this::convertToLocalDate);
        String bugDescriptionCheck = getCustomFieldValue(issue, FIELD_BUG_DESCRIPTION_CHECK, this::extractStringValue);

        IssueEntity issueEntity = new IssueEntity(
                id, summary, status, priority, issueType, created, projectId, projectName, projectKey, issueKey,
                issueSource, issueModule, issueSubModule, fixVersionDate, fixVersion, dueDate, storyPoints, resolution,
                resolved, userViewCategory, affectsPlatform, updated, inDate, purpose, rfqPlanDate, rfqDate,
                planReleaseDate, reporter, developer, tester, originalEstimate, smokeCheck, legacyIssue, qaTeam,
                workCategory, parentKey, rfqDelayComment, releaseDelayComment, assignee, additionalRequirements,
                parentId, timeSpent, etaShanghai, remark, components, bugDescriptionCheck
        );
        log.info("issueEntity: {}", issueEntity);
        return new JiraIngestionPayload(issueEntity, null, null);
    }

    // region Generic Helper Method
    /**
     * 通用的自定义字段获取和转换方法
     *
     * @param issue      Jira Issue 对象
     * @param fieldId    自定义字段 ID
     * @param transformer 用于将字段原始值 (Object) 转换为目标类型 (T) 的函数
     * @param <T>        目标类型
     * @return 转换后的值，如果字段不存在或转换失败则返回 null
     */
    private <T> T getCustomFieldValue(Issue issue, String fieldId, Function<Object, T> transformer) {
        try {
            return Optional.ofNullable(issue.getField(fieldId))
                    .map(IssueField::getValue)
                    .map(transformer)
                    .orElse(null);
        } catch (Exception e) {
            // 捕获更具体的异常，或在日志中提供更详细的上下文
            log.warn("Failed to parse custom field '{}' for issue {}. Reason: {}", fieldId, issue.getKey(), e.getMessage());
            return null;
        }
    }
    // endregion

    // region Type Conversion and Extraction Helpers
    private String extractStringValue(Object fieldValue) {
        if (fieldValue == null) return null;
        if (fieldValue instanceof String) {
            return (String) fieldValue;
        }
        if (fieldValue instanceof JSONObject) {
            return ((JSONObject) fieldValue).optString("value", null);
        }
        // 对于其他类型（如Number），也转换为字符串
        return fieldValue.toString();
    }

    private String extractChildValue(Object fieldValue, String childKey) {
        if (fieldValue instanceof JSONObject json) {
            try {
                // 尝试将子字段作为对象获取，再取其 value
                if (json.has(childKey) && json.get(childKey) instanceof JSONObject childObj) {
                    return childObj.optString("value", childObj.toString());
                }
                // 否则直接将子字段作为字符串获取
                return json.optString(childKey, null);
            } catch (JSONException e) {
                log.warn("Failed to extract child value for key '{}' from JSON: {}", childKey, json, e);
                return null;
            }
        }
        return null;
    }

    private Decimal convertToDecimal(Object fieldValue) {
        String stringValue = extractStringValue(fieldValue);
        if (stringValue == null || stringValue.isBlank()) return null;
        try {
            return Decimal.valueOf(stringValue);
        } catch (NumberFormatException e) {
            log.warn("Could not parse '{}' to Decimal.", stringValue, e);
            return null;
        }
    }

    private Integer convertToInteger(Object fieldValue) {
        String stringValue = extractStringValue(fieldValue);
        if (stringValue == null || stringValue.isBlank()) return null;
        try {
            return (int) Double.parseDouble(stringValue);
        } catch (NumberFormatException e) {
            log.warn("Could not parse '{}' to Integer.", stringValue, e);
            return null;
        }
    }

    private OffsetDateTime convertToOffsetDateTime(Object fieldValue) {
        String stringValue = extractStringValue(fieldValue);
        if (stringValue == null || stringValue.isBlank()) return null;
        try {
            return convertDateTime(DateTime.parse(stringValue));
        } catch (IllegalArgumentException e) {
            log.warn("Could not parse '{}' to DateTime.", stringValue, e);
            return null;
        }
    }

    private LocalDate convertToLocalDate(Object fieldValue) {
        OffsetDateTime offsetDateTime = convertToOffsetDateTime(fieldValue);
        return offsetDateTime != null ? offsetDateTime.toLocalDate() : null;
    }

    private OffsetDateTime convertDateTime(DateTime dateTime) {
        return dateTime == null ? null : OffsetDateTime.ofInstant(Instant.ofEpochMilli(dateTime.getMillis()), ZoneOffset.UTC);
    }

    private LocalDate convertLocalDate(DateTime dateTime) {
        return dateTime == null ? null : convertDateTime(dateTime).toLocalDate();
    }

    private Optional<Version> getMaxReleaseVersion(Issue issue) {
        if (issue.getFixVersions() == null) {
            return Optional.empty();
        }
        return StreamSupport.stream(issue.getFixVersions().spliterator(), false)
                .filter(version -> version.getReleaseDate() != null)
                .max(Comparator.comparing(Version::getReleaseDate));
    }

    private String getComponentsAsString(Issue issue) {
        if (issue.getComponents() == null) {
            return null;
        }
        String result = StreamSupport.stream(issue.getComponents().spliterator(), false)
                .map(BasicComponent::getName)
                .collect(Collectors.joining(","));
        return result.isEmpty() ? null : result;
    }
}