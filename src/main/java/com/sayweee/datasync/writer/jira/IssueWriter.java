package com.sayweee.datasync.writer.jira;

import com.sayweee.datasync.common.constants.SyncConstants;
import com.sayweee.datasync.model.entity.IssueEntity;
import com.sayweee.datasync.dao.IssueTempDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class IssueWriter {

    private final IssueTempDao issueTempDao;

    /**
     * 批量写入或更新 Redshift 中的 JIRA Issues
     */
    public void write(String taskId, List<IssueEntity> issueEntities) {
        if (issueEntities == null || issueEntities.isEmpty()) {
            log.warn("Task [{}] - No payloads to write", taskId);
            return;
        }

        log.info("Task [{}] - Starting to write {} issues to Redshift using MyBatis", taskId, issueEntities.size());

        try {
            int affectedRows = batchUpsertIssues(taskId, issueEntities);
            log.info("Task [{}] - Successfully processed {} issues, affected {} rows",
                    taskId, issueEntities.size(), affectedRows);

        } catch (Exception e) {
            log.error("Task [{}] - Failed to write issues to Redshift", taskId, e);
            throw new RuntimeException("Failed to write issues to database", e);
        }
    }

    /**
     * 批量 UPSERT Issues 到 Redshift (完全使用 MyBatis)
     */
    private int batchUpsertIssues(String taskId, List<IssueEntity> issueEntities) {
        log.debug("Task [{}] - Preparing batch upsert for {} issues", taskId, issueEntities.size());

        String tempTableName = createTempTable(taskId);

        try {
            batchInsertToTempTable(taskId, tempTableName, issueEntities);
            return executeUpsertFromTempTable(taskId, tempTableName);
        } finally {
            dropTempTable(taskId, tempTableName);
        }
    }

    /**
     * 【MyBatis 实现】创建临时表
     */
    private String createTempTable(String taskId) {
        String tempTableName = "weee_jira_new.temp_jira_issues_" + taskId.replaceAll("[^a-zA-Z0-9_]", "") + "_" + System.currentTimeMillis();
        try {
            issueTempDao.createTempTableLike(tempTableName, "weee_jira_new.issue");
            log.debug("Task [{}] - Created temp table: {}", taskId, tempTableName);
            return tempTableName;
        } catch (Exception e) {
            log.error("Task [{}] - Failed to create temp table using MyBatis", taskId, e);
            throw new RuntimeException("Failed to create temp table", e);
        }
    }

    /**
     * 【MyBatis 实现】批量插入数据到临时表
     */
    private void batchInsertToTempTable(String taskId, String tempTableName, List<IssueEntity> issueEntities) {
        log.debug("Task [{}] - Inserting {} records into temp table {} using MyBatis", taskId, issueEntities.size(), tempTableName);
        try {
            issueTempDao.batchInsert(tempTableName, issueEntities);
            log.debug("Task [{}] - Successfully inserted records into temp table {}", taskId, tempTableName);
        } catch (Exception e) {
            log.error("Task [{}] - Failed to insert data into temp table using MyBatis: {}", taskId, tempTableName, e);
            throw new RuntimeException("Failed to insert data into temp table with MyBatis", e);
        }
    }

    /**
     * 【MyBatis 实现】执行 UPSERT 操作 (DELETE + INSERT)
     */
    private int executeUpsertFromTempTable(String taskId, String tempTableName) {
        try {
            String targetTable = SyncConstants.Database.JIRA_ISSUES_TABLE;

            log.debug("Task [{}] - Executing DELETE phase of UPSERT via MyBatis...", taskId);
            int deletedRows = issueTempDao.deleteFromTargetUsingTemp(targetTable, tempTableName);
            log.debug("Task [{}] - Deleted {} existing rows from target table.", taskId, deletedRows);

            log.debug("Task [{}] - Executing INSERT phase of UPSERT via MyBatis...", taskId);
            int insertedRows = issueTempDao.insertFromTempToTarget(targetTable, tempTableName);
            log.debug("Task [{}] - Inserted {} new/updated rows into target table.", taskId, insertedRows);

            return deletedRows + insertedRows;
        } catch (Exception e) {
            log.error("Task [{}] - Failed to execute UPSERT (DELETE + INSERT) operation via MyBatis", taskId, e);
            throw new RuntimeException("Failed to execute UPSERT operation with MyBatis", e);
        }
    }

    /**
     * 【MyBatis 实现】清理临时表
     */
    private void dropTempTable(String taskId, String tempTableName) {
        try {
            issueTempDao.dropTable(tempTableName);
            log.debug("Task [{}] - Dropped temp table: {}", taskId, tempTableName);
        } catch (Exception e) {
            log.warn("Task [{}] - Failed to drop temp table: {}. This might require manual cleanup.",
                    taskId, tempTableName, e);
        }
    }
}