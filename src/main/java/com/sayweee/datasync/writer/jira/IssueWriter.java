package com.sayweee.datasync.writer.jira;

import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class IssueWriter {
    public void write(String taskId, List<JiraIngestionPayload> payloads) {
        log.info("Writing issues for task {}", taskId);
    }
}
