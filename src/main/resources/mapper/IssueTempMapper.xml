<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sayweee.datasync.dao.IssueTempDao">

    <insert id="batchInsert">
    </insert>

    <delete id="deleteFromTargetUsingTemp">
        DELETE FROM ${targetTable}
            USING ${tempTable}
        WHERE ${targetTable}.id = ${tempTable}.id
    </delete>

    <insert id="insertFromTempToTarget">
        INSERT INTO weee_jira_new.issue
        SELECT * FROM ${tempTable}
    </insert>

    <update id="createTempTableLike">
        CREATE TABLE ${tempTable} as select * from ${targetTable} where 1=0
    </update>

    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

</mapper>